<?php

namespace App\Http\Controllers\Api\Order;

use App\Http\Controllers\Api\Service\SmmController;
use App\Http\Controllers\Api\Service\TuongtaccheoController;
use App\Http\Controllers\Api\Service\TraodoisubController;

use App\Http\Controllers\Controller;
use App\Library\DiscordSdk;
use App\Models\Order;
use App\Models\Smm;
use App\Models\Voucher;
use App\Models\PartnerWebsite;
use App\Models\ServerAction;
use App\Models\Service;
use App\Models\ServiceServer;
use App\Models\ServicePlatform;
use App\Models\Transaction;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class OrderController extends Controller
{

    public function createOrder(Request $request) {
       

            if (site('maintain') === 'on') {
                return response()->json([
                    'code' => '401',
                    'status' => 'error',
                    'message' => 'Hệ thống đang bảo trì, vui lòng quay lại sau !',
                ], 401);
            }
            
            $api_token = $request->header('X-Access-Token');

            if (!$api_token) {
                return response()->json([
                    'code' => '401',
                    'status' => 'error',
                    'message' => 'Không tìm thấy X-Access-Token !',
                ], 401);
            }

            $domain = $request->getHost();
            $user = User::where('api_token', $api_token)->where('domain', $domain)->first();

            if (!$user) {
                return response()->json([
                    'code' => '401',
                    'status' => 'error',
                    'message' => 'X-Access-Token không hợp lệ !',
                ], 401);
            }

            if ($user->status !== 'active') {
                return response()->json([
                    'code' => '401',
                    'status' => 'error',
                    'message' => 'Tài khoản của bạn hiện tại không được phép thực hiện hành động này !',
                ], 401);
            }

            $valid = Validator::make($request->all(), [
                'provider_server' => 'required',
            ], [
                'provider_server.required' => 'Vui lòng chọn máy chủ cần mua!',
            ]);

            if ($valid->fails()) {
                return response()->json([
                    'code' => '400',
                    'status' => 'error',
                    'message' => $valid->errors()->first(),
                ], 400);
            }

            if ($domain === env('APP_MAIN_SITE')) {
                $service = Service::where('package', $request->provider_package)->where('domain', env('APP_MAIN_SITE'))->first();
                if (!$service) {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Gói cần mua không tồn tại !',
                    ], 400);
                }

                if ($service->status !== 'active') {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Gói cần mua hiện không khả dụng !',
                    ], 400);
                }
                $service_platform = ServicePlatform::where('id', $service->platform_id)->first();
                $child_panel_server = str_replace('sv-', '', $request->provider_server);


                $lam12 = explode("_", $child_panel_server);
                $provider_server = $lam12[0];
                $server = ServiceServer::where('service_id', $service->id)->where('package_id', $provider_server)->where('domain', $domain)->first();

                if (!$server) {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Máy chủ này không tồn tại !',
                    ], 400);
                }

                if ($server->visibility !== 'public') {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Máy chủ này không khả dụng !',
                    ], 400);
                }
                $serverAction = ServerAction::where('server_id', $server->id)->first();

                if (!$serverAction) {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Máy chủ này hiện đang bị lỗi vui lòng thử lại sau !',
                    ], 400);
                }
                if($serverAction->quantity_like !=='on'){
                    $quantity =  $request->quantity;
                }
               else{
                   $quantity = $request->quantity_like;
                    
               }


                if ($server->status !== 'active') {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Máy chủ này hiện đang bảo trì !',
                    ], 400);
                }

                $validService = Validator::make($request->all(), [
                    'object_id' => 'required',
                ], [
                    'object_id.required' => 'Vui lòng nhập UID hoặc Link cần mua !',
                ]);

                if ($validService->fails()) {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => $validService->errors()->first(),
                    ], 400);
                }
                
                $object_id = $request->object_id;
                if (is_string($object_id) && $serverAction->get_uid === 'on') {
                    $facebookDomains = ['facebook.com','fb.com',];
                    foreach ($facebookDomains as $fb) {
                        if (strpos($object_id, $fb) !== false) {
                            $id = $this->handleFacebookID($object_id);
                            if ($id === null) {
                                $id = $object_id;
                            }
                            $object_id = $id;
                            break;
                        }
                    }
                }                                          
                                
                if ($serverAction->quantity_status === 'on') {
                    $valid = Validator::make($request->all(), [
                        'quantity' => 'required|integer'
                    ], [
                        'quantity.required' => 'Vui lòng chọn số lượng cần mua !',
                        'quantity.integer' => 'Số lượng cần mua phải là số!',
                    ]);

                    if ($valid->fails()) {
                        return response()->json([
                            'code' => '400',
                            'status' => 'error',
                            'message' => $valid->errors()->first(),
                        ], 400);
                    }
                }

                if ($serverAction->reaction_status === 'on') {
                    $valid = Validator::make($request->all(), [
                        'reaction' => 'required'
                    ], [
                        'reaction.required' => 'Vui lòng chọn cảm xúc!',
                    ]);

                    if ($valid->fails()) {
                        return response()->json([
                            'code' => '400',
                            'status' => 'error',
                            'message' => $valid->errors()->first(),
                        ], 400);
                    }
                }

                if ($serverAction->comments_status === 'on') {
                    $valid = Validator::make($request->all(), [
                        'comments' => 'required'
                    ], [
                        'comments.required' => 'Vui lòng nhập nội dung bình luận!',
                    ]);

                    if ($valid->fails()) {
                        return response()->json([
                            'code' => '400',
                            'status' => 'error',
                            'message' => $valid->errors()->first(),
                        ], 400);
                    }

                    $count = 0;
                    $comments = explode("\n", $request->comments);
                    $comments = array_filter($comments, 'trim');
                    $comments = array_values($comments);
                    $count = count($comments);
                    $quantity = $count;
                    $request->merge(['quantity' => $count]);
                }

                if ($serverAction->minutes_status === 'on') {
                    $valid = Validator::make($request->all(), [
                        'minutes' => 'required|integer'
                    ], [
                        'minutes.required' => 'Vui lòng chọn số phút cần mua!',
                        'minutes.integer' => 'Số phút cần mua phải là số!',
                    ]);

                    if ($valid->fails()) {
                        return response()->json([
                            'code' => '400',
                            'status' => 'error',
                            'message' => $valid->errors()->first(),
                        ], 400);
                    }
                }

                if ($serverAction->posts_status === 'on') {
                    $valid = Validator::make($request->all(), [
                        'posts' => 'required'
                    ], [
                        'posts.required' => 'Vui lòng chọn số bài viết cần mua!',
                    ]);

                    if ($valid->fails()) {
                        return response()->json([
                            'code' => '400',
                            'status' => 'error',
                            'message' => $valid->errors()->first(),
                        ], 400);
                    }

                    $newPost = $request->posts == 'unlimited' ? 1 : $request->posts;
                    $request->merge(['posts' => $newPost]);
                }

                if ($serverAction->time_status === 'on') {
                    $valid = Validator::make($request->all(), [
                        'duration' => 'required|integer'
                    ], [
                        'duration.required' => 'Vui lòng chọn số bài viết cần mua!',
                        'duration.integer' => 'Số bài viết cần mua phải là số!',
                    ]);

                    if ($valid->fails()) {
                        return response()->json([
                            'code' => '400',
                            'status' => 'error',
                            'message' => $valid->errors()->first(),
                        ], 400);
                    }
                }

                if ($server->limit_day > 0) {
                    $orderToday = Order::where('server_id', $server->id)->whereDate('created_at', Carbon::today())->count();

                    if ($orderToday >= $server->limit_day) {
                        return response()->json([ 'code' => '400', 'status' => 'error', 'message' => 'Máy chủ này đã đạt giới hạn mua hàng trong ngày!', ], 400);
                    }
                }
                

                if ($quantity < $server->min) {
                    return response()->json([ 'code' => '400', 'status' => 'error', 'message' => 'Số lượng cần mua phải lớn hơn hoặc bằng ' . $server->min . ' !', ], 400);
                }

                if ($quantity > $server->max) {
                    return response()->json([ 'code' => '400', 'status' => 'error', 'message' => 'Số lượng cần mua phải nhỏ hơn hoặc bằng ' . $server->max . ' !', ], 400);
                }

                $price = $server->levelPrice($user->level);
                $rate_profit = $server->price;

                
                $posts = max(1, (int) $request->posts);
                $duration = max(1, (int) $request->duration);
                $minutes = max(1, (int) $request->minutes);
                
                $multiplier = 1;
                
                if ($serverAction->time_status === 'on') {
                    $multiplier *= $duration;
                }
                
                if ($serverAction->posts_status === 'on') {
                    $multiplier *= $posts;
                }
                
                if ($serverAction->minutes_status === 'on') {
                    $multiplier *= $minutes;
                }
                
                $total = $price * $quantity * $multiplier;
                $total_profit = $rate_profit * $quantity * $multiplier;
                

                if ($user->balance < ceil($total)) {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Số dư của bạn không đủ để thực hiện giao dịch này!',
                    ], 400);
                }

                
                if (isset($server->percents)) {
                    $quantity = $quantity * ($server->percents / 100);
                } else {
                    $quantity = $quantity;
                }

                $orderID = null;
                $orderCode = site('madon') . '_' . time() . rand(1000, 9999);

                $orderData = [
                    "user_id" => $user->id,
                    "service_id" => $service->id,
                    "server_id" => $server->id,
                    "order_code" => "",
                    "object_id" => $request->object_id,
                    "quantity" => $quantity,
                    "reaction" => $request->reaction,
                    "comments" => htmlentities($request->comments),
                    "minutes" => $request->minutes,
                    'posts' => $request->posts,
                    'duration' => $request->duration,
                    "price" => $price,
                    'payment' => $total,
                    'total_profit' => $total_profit,
                    'note' => $request->note,
                ];

                if($request->cb_schedule == 'yes' || site('maintain') === 'hold_order'){
                    $orderID = 'Đơn hẹn giờ';
                    $order_status = 'Pending';
                    $orderData["order_code"] = $orderCode;
                } else {
                    if ($server->providerName == 'traodoisub') {
                        $orderData["order_code"] = $orderCode;

                        $tds = new TraodoisubController();
                        $tds->path = $server->providerLink;
                        $tds->data = [
                            'object_id' => $request->object_id,
                            'server_order' => $server->providerServer,
                            'quantity' => $quantity,
                            'quantity_like' => $quantity,
                            'reaction' => $request->reaction,
                            'speed' => 1,
                            'comment' => $request->comments,
                            'order_codes' => $orderCode,
                            'minutes' => $request->minutes,
                            'days' => $request->duration,
                            'post' => $request->posts,
                        ];

                        $result = $tds->createOrder();
                        if (isset($result['status']) && $result['status'] === true) {
                            $orderID = $result['data'];
                        } else {
                            return response()->json([
                                'code' => '400',
                                'status' => 'error',
                                'message' => $result['message'],
                            ], 400);
                        }
                    } else if ($server->providerName == 'tuongtaccheo') {
                        $orderData["order_code"] = $orderCode;

                        $tds = new TuongtaccheoController();
                        $tds->path = $server->providerLink;
                        $tds->data = [
                            'object_id' => $request->object_id,
                            'server_order' => $server->providerServer,
                            'quantity' => $quantity,
                            'reaction' => $request->reaction,
                            'speed' => 1,
                            'comment' => $request->comments,
                            'order_code_api' => $orderCode,
                            'minutes' => $request->minutes,
                            'days' => $request->duration,
                            'post' => $request->posts,
                        ];

                        $result = $tds->createOrder();
                        if (isset($result['status']) && $result['status'] === true) {
                            $orderID = $orderCode;
                        } else {
                            return response()->json([
                                'code' => '400',
                                'status' => 'error',
                                'message' => $result['message'],
                            ], 400);
                        }
                    } elseif ($server->providerName == 'tuongtacsieutoc') {
                        $orderData["order_code"] = $orderCode;
                        
                        $TTST = new TuongTacSieuTocController();
                        $TTST->path = $server->providerLink;
                        $TTST->data = [
                            'url' => $request->object_id,
                            'service_pack' => $server->providerServer,
                            'quantity' => $quantity,
                            'note' => $orderCode,
                            'comment' => $request->comments,
                            'feeling' => $request->reaction,
                        ];

                        $result = $TTST->CreateOrder();
                        if (isset($result) && $result['status'] == true) {
                            $orderID = $orderCode;
                        } else {
                            return response()->json([
                                'code' => '400',
                                'status' => 'error',
                                'message' => $result['message'],
                            ], 400);
                        }
                    } elseif ($server->providerName == 'tanglikecheo') {
                        $orderData["order_code"] = $orderCode;
                        
                        $TLC = new TanglikecheoController();
                        $TLC->path = $server->providerLink;
                        $TLC->data = [
                            "object_id" => $request->object_id,
                            "type" => $server->providerLink,
                            "price" => "",
                            "prices" => "",
                            "provider" => $server->providerKey,
                            "speed" => 0,
                            "quantity" => $quantity,
                            "package_type" => $server->providerServer,
                            "object_type" => $request->reaction,
                            "notes" => $request->note,
                        ];
                    
                        $result = $TLC->CreateOrder($TLC->data);
                        Log::debug('Tanglikecheo Order:', $result);
                        if (isset($result['status']) && $result['status'] == true) {
                            $orderID = $result['data']['id'];
                            $orderData['order_id'] = $orderID; 
                        } else {
                            return response()->json([
                                'code' => '400',
                                'status' => 'error',
                                'message' => $result['message'],
                            ], 400);
                        }
                    } elseif ($server->providerName == 'dontay') {
                        $orderID = time() . rand(1000, 9999);
                        $orderData["order_code"] = $orderCode;
                    } else {
                        $smm = Smm::where('domain', env('APP_MAIN_SITE'))->get();
                        foreach ($smm as $smms) {
                            if ($server->providerName == $smms['name']) {
                                $path = $smms['name'];
                                $curlSMM = new SmmController;
                                $post = array(
                                    'key' => $smms['token'],
                                    'action' => 'add',
                                    'service' => $server->providerServer,
                                    'link' => $request->object_id,
                                    'minutes' => $request->minutes,
                                    'quantity' => $quantity,
                                    'comments' => $request->comments,
                                    'reaction' => strtolower($request->reaction) ?? 'like'
                                );
                                $result = curl_smm($path, $post);
                                if (isset($result['order']) && !empty($result['order'])) {
                                    $orderID = $result['order'];
                                    $orderCode = site('madon') . '_' . time() . rand(1000, 9999);
                                    $orderData["order_code"] = $orderCode;
                                } else {
                                    if (isset($result['error']) && $result['error']== 'neworder.error.link_duplicate') {
                                        $msg = 'Liên kết này đang hoạt động trên hệ thống. Vui lòng đợi đơn hàng hoàn thành !';
                                        return response()->json([
                                        'code' => '400',
                                        'status' => 'error',
                                        'message' => $msg,
                                    ], 400);
                                    }else if (isset($result['error']) && $result['error'] == 'Not enough balance!' || isset($result['error']) && $result['error']== 'Insufficient account balance') {
                                        $orderID = time() . rand(1000, 9999);
                                        $orderData["order_code"] = $orderCode;
                                        $order_status = "Pending_Balance";
                                    }
                                    else {
                                        $msg = 'Máy chủ hết tài nguyên, vui lòng liên hệ admin để được hỗ trợ !';
                                        return response()->json([
                                        'code' => '400',
                                        'status' => 'error',
                                        'message' => $result['error'],
                                    ], 400);
                                    }

                                    
                                }
                            }
                        }
                    }
                }

                $order = new Order();
                $order->user_id = $user->id;
                $order->service_id = $service->id;
                $order->server_id = $server->id;
                $order->cb_loop = $request->cb_loop ?? "no";
                $order->cb_schedule = $request->cb_schedule ?? "no";
                $order->time_order = $request->time_order ?? now();
                $order->time_loop = $request->time_loop ?? 0;
                $order->quantity_loop = $request->quantity_loop ?? 0;
                $order->orderProviderName = $server->providerName;
                $order->orderProviderPath = $server->providerLink;
                $order->orderProviderServer = $server->providerServer;
                $order->order_package = $service->package;
                $order->object_server = $child_panel_server;
                $order->object_id = $object_id;
                $order->order_id = $orderID;
                $order->order_code = $orderCode;
                $order->order_data = json_encode($orderData);
                $order->start = 0;
                $order->buff = 0;
                $order->duration = $request->duration;
                $order->posts = $request->posts;
                $order->remaining = $request->duration;
                $order->price = $price;
                $order->payment = $total;
                $order->total_profit = $total - $total_profit;
                $order->status = $order_status ?? 'Processing';
                $order->ip = $request->ip();
                $order->note = $request->note;
                $order->time = now();
                $order->domain = $domain;
                $order->save();

                if ($order) {
                    if ($user->balance < $total) {
                        $user->status = 'banned';
                        $user->save();
                        return response()->json([
                            'code' => '400',
                            'status' => 'error',
                            'message' => 'Tài khoản của bạn đã bị khoá do thực hiện giao dịch không hợp lệ!',
                        ], 400);
                    }

                    $transaction = new Transaction();
                    $transaction->user_id = $user->id;
                    $transaction->tran_code = $orderCode;
                    $transaction->type = 'order';
                    $transaction->action = 'sub';
                    $transaction->first_balance = $total;
                    $transaction->before_balance = $user->balance;
                    $transaction->after_balance = $user->balance - $total;
                    $transaction->note = 'Thanh toán đơn hàng ' . $orderCode;
                    $transaction->ip = $request->ip();
                    $transaction->domain = $domain;
                    $transaction->save();

                    $user->decrement('balance', $total);

                    // Đơn tay đã được xóa - không cần thông báo riêng

                    Log::info("Đơn hàng web mẹ được tạo thành công", [
                        'order_code' => $orderCode,
                        'user_id' => $user->id,
                        'service_id' => $service->id,
                        'server_id' => $server->id,
                        'total' => $total,
                        'provider' => $server->providerName,
                        'domain' => $domain
                    ]);

                    $this->sendDiscordNotifications($orderData, $user, $service, $server, $price, $total, $domain, $request);

                    return response()->json([
                        'code' => '200',
                        'status' => 'success',
                        'message' => 'Đơn hàng của bạn đã được tạo thành công!',
                        'data' => [
                            'id' => $order->id,
                            'order_code' => $orderCode,
                            'price' => $price,
                            'payment' => $total,
                            'status' => 'Processing',
                        ],
                    ], 200);
                }
            } else {
                $partner = PartnerWebsite::where('name', getDomain())->first();
                $admin = User::where('id', $partner->user_id)->first();
                if (!$admin) {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Không tìm thấy tài khoản Sdmin!',
                    ], 400);
                }

                $service = Service::where('package', $request->provider_package)->where('domain', env('APP_MAIN_SITE'))->first();

                if (!$service) {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Gói cần mua không tồn tại!',
                    ], 400);
                }

                if ($service->status !== 'active') {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Gói cần mua hiện không khả dụng!',
                    ], 400);
                }

                $child_panel_server = str_replace('sv-', '', $request->provider_server);

                $lam12 = explode("_", $child_panel_server);
                $provider_server = $lam12[0];

                $server = ServiceServer::where('service_id', $service->id)->where('package_id', $provider_server)->where('domain', $domain)->first();
                $server_admin = ServiceServer::where('service_id', $service->id)->where('package_id', $server->package_id)->where('domain', $partner->domain)->first();

                if (!$server && !$server_admin) {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Máy chủ này không tồn tại !',
                    ], 400);
                }

                if ($server->visibility !== 'public' && $server_admin->visibility !== 'public') {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Máy chủ này không khả dụng !',
                    ], 400);
                }

                if ($server->status !== 'active' && $server_admin->status !== 'active') {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Máy chủ này hiện đang bảo trì !',
                    ], 400);
                }
                $serverAction = ServerAction::where('server_id', $server->id)->first();

                if (!$serverAction) {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Máy chủ này hiện đang bị lỗi vui lòng thử lại sau !',
                    ], 400);
                }
                if($serverAction->quantity_like !=='on'){
                    $quantity =  $request->quantity;
                }
               else{
                   $quantity = $request->quantity_like;
                   $request->quantity = $request->quantity_like;

                    
               }

                $validService = Validator::make($request->all(), [
                    'object_id' => 'required',
                ], [
                    'object_id.required' => 'Vui lòng nhập UID hoặc Link cần mua !',
                ]);

                if ($validService->fails()) {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => $validService->errors()->first(),
                    ], 400);
                }

                $object_id = $request->object_id;
                if (is_string($object_id) && $serverAction->get_uid === 'on') {
                    $facebookDomains = ['facebook.com','fb.com',];
                    foreach ($facebookDomains as $fb) {
                        if (strpos($object_id, $fb) !== false) {
                            $id = $this->handleFacebookID($object_id);
                            if ($id === null) {
                                $id = $object_id;
                            }
                            $object_id = $id;
                            break;
                        }
                    }
                }                           

                if ($serverAction->quantity_status === 'on') {
                    $valid = Validator::make($request->all(), [
                        'quantity' => 'required|integer'
                    ], [
                        'quantity.required' => 'Vui lòng chọn số lượng cần mua !',
                        'quantity.integer' => 'Số lượng cần mua phải là số !',
                    ]);

                    if ($valid->fails()) {
                        return response()->json([
                            'code' => '400',
                            'status' => 'error',
                            'message' => $valid->errors()->first(),
                        ], 400);
                    }
                }

                if ($serverAction->reaction_status === 'on') {
                    $valid = Validator::make($request->all(), [
                        'reaction' => 'required'
                    ], [
                        'reaction.required' => 'Vui lòng cảm xúc !',
                    ]);

                    if ($valid->fails()) {
                        return response()->json([
                            'code' => '400',
                            'status' => 'error',
                            'message' => $valid->errors()->first(),
                        ], 400);
                    }
                }

                if ($serverAction->comments_status === 'on') {
                    $valid = Validator::make($request->all(), [
                        'comments' => 'required'
                    ], [
                        'comments.required' => 'Vui lòng nhập nội dung bình luận !',
                    ]);

                    if ($valid->fails()) {
                        return response()->json([
                            'code' => '400',
                            'status' => 'error',
                            'message' => $valid->errors()->first(),
                        ], 400);
                    }

                    $count = 0;
                    $comments = explode("\n", $request->comments);
                    $comments = array_filter($comments, 'trim');
                    $comments = array_values($comments);
                    $count = count($comments);
                    $quantity = $count;
                    $request->merge(['quantity' => $count]);
                }

                if ($serverAction->minutes_status === 'on') {
                    $valid = Validator::make($request->all(), [
                        'minutes' => 'required|integer'
                    ], [
                        'minutes.required' => 'Vui lòng chọn Số Phút cần mua !',
                        'minutes.integer' => 'Số Phút cần mua phải là số !',
                    ]);

                    if ($valid->fails()) {
                        return response()->json([
                            'code' => '400',
                            'status' => 'error',
                            'message' => $valid->errors()->first(),
                        ], 400);
                    }
                }

                if ($serverAction->posts_status === 'on') {
                    $valid = Validator::make($request->all(), [
                        'posts' => 'required'
                    ], [
                        'posts.required' => 'Vui lòng chọn số bài viết cần mua !',
                    ]);

                    if ($valid->fails()) {
                        return response()->json([
                            'code' => '400',
                            'status' => 'error',
                            'message' => $valid->errors()->first(),
                        ], 400);
                    }
                    $newPost = $request->posts == 'unlimited' ? 1 : $request->posts;
                    $request->merge(['posts' => $newPost]);
                }

                if ($serverAction->time_status === 'on') {
                    $valid = Validator::make($request->all(), [
                        'duration' => 'required|integer'
                    ], [
                        'duration.required' => 'Vui lòng chọn số bài viết cần mua !',
                        'duration.integer' => 'Số bài viết cần mua phải là số !',
                    ]);

                    if ($valid->fails()) {
                        return response()->json([
                            'code' => '400',
                            'status' => 'error',
                            'message' => $valid->errors()->first(),
                        ], 400);
                    }
                }

                if ($server->limit_day > 0) {
                    $orderToday = Order::where('server_id', $server->id)->whereDate('created_at', Carbon::today())->count();

                    if ($orderToday >= $quantity) {
                        return response()->json([
                            'code' => '400',
                            'status' => 'error',
                            'message' => 'Máy chủ này đã đạt giới hạn mua hàng trong ngày !',
                        ], 400);
                    }
                }
                

                if ($quantity < $server->min) {
                    return response()->json([ 'code' => '400', 'status' => 'error', 'message' => 'Số lượng cần mua phải lớn hơn hoặc bằng ' . $server->min . ' !', ], 400);
                }

                if ($quantity > $server->max) {
                    return response()->json([ 'code' => '400', 'status' => 'error', 'message' => 'Số lượng cần mua phải nhỏ hơn hoặc bằng ' . $server->max . ' !', ], 400);
                } 
                $price = $server->levelPrice($user->level);
                $rate_profit = $server->price;
                $price_admin = $server_admin->levelPrice($admin->level);
                
                 
                $posts = max(1, (int) $request->posts);
                $duration = max(1, (int) $request->duration);
                $minutes = max(1, (int) $request->minutes);
                
                $multiplier = 1;
                
                if ($serverAction->time_status === 'on') {
                    $multiplier *= $duration;
                }
                
                if ($serverAction->minutes_status === 'on') {
                    $multiplier *= $minutes;
                }
                
                if ($serverAction->posts_status === 'on') {
                    $multiplier *= $posts;
                }
                
                $total = $price * $quantity * $multiplier;
                $total_admin = $price_admin * $quantity * $multiplier;
                $total_profit = $rate_profit * $quantity * $multiplier;
                
                if ($user->balance < ceil($total)) {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Số dư của bạn không đủ để thực hiện giao dịch này !',
                    ], 400);
                }

                if ($admin->balance < ceil($total_admin)) {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => 'Số dư của bạn không đủ để thực hiện giao dịch này !',
                    ], 400);
                }

                $urlOrder = "http://" . $partner->domain . "/api/v1/start/create/order";
                
                $dataSend = [
                    'provider_package' => $request->provider_package,
                    'provider_server' => $provider_server,
                    'object_id' => $request->object_id,
                    'quantity' => $quantity,
                    'reaction' => $request->reaction,
                    'comments' => $request->comments,
                    'minutes' => $request->minutes,
                    'posts' => $request->posts,
                    'duration' => $request->duration,
                    'note' => $request->getHost() . ' - Khởi tạo đơn hàng từ API',
                ];
                
                $response = Http::withHeaders([
                    'X-Access-Token' => $admin->api_token,
                    'Content-Type' => 'application/json',
                ])->post($urlOrder, $dataSend);
                
                $result = $response->json();
                
                if (isset($result) && $result['status'] == 'success') {
                    $orderID = $result['data']['id'];
                    $orderCode = site('madon') . time() . rand(1000, 9999);

                    // Bắt đầu database transaction để đảm bảo tính toàn vẹn
                    DB::beginTransaction();

                    try {
                        $orderData = [
                            "user_id" => $user->id,
                            "service_id" => $service->id,
                            "server_id" => $server->id,
                            "order_code" => $orderCode,
                            "object_id" => $request->object_id,
                            "quantity" => $quantity,
                            "reaction" => $request->reaction,
                            "comments" => htmlentities($request->comments),
                            "minutes" => $request->minutes,
                            "price" => $price,
                            'payment' => $total,
                            'note' => $request->note,
                        ];

                        $order = new Order();
                        $order->user_id = $user->id;
                        $order->service_id = $service->id;
                        $order->server_id = $server->id;
                        $order->orderProviderName = $partner->domain;
                        $order->orderProviderServer = $server->providerServer;
                        $order->order_package = $service->package;
                        $order->object_server = $provider_server;
                        $order->object_id = $request->object_id;
                        $order->order_id = $orderID;
                        $order->order_code = $orderCode;
                        $order->order_data = json_encode($orderData);
                        $order->start = $quantity;
                        $order->buff = 0;
                        $order->duration = $request->duration;
                        $order->posts = $request->posts;
                        $order->remaining = $request->duration;
                        $order->price = $price;
                        $order->payment = $total;
                        $order->total_profit = $total - $total_profit;
                        $order->status = $order_status ?? 'Processing';
                        $order->ip = $request->ip();
                        $order->note = $request->note;
                        $order->time = now();
                        $order->domain = $domain;
                        $order->save();

                        if ($order) {

                            if ($user->balance < $total) {
                                $user->status = 'banned';
                                $user->save();
                                return response()->json([ 'code' => '400', 'status' => 'error', 'message' => 'Tài khoản của bạn đã bị khoá do thực hiện giao dịch không hợp lệ!',
                                ], 400);
                            }

                            $transaction = new Transaction();
                            $transaction->user_id = $user->id;
                            $transaction->tran_code = $orderCode;
                            $transaction->type = 'order';
                            $transaction->action = 'sub';
                            $transaction->first_balance = $user->balance;
                            $transaction->before_balance = $total;
                            $transaction->after_balance = $user->balance - $total;
                            $transaction->note = 'Thanh toán đơn hàng ' . $orderCode;
                            $transaction->ip = $request->ip();
                            $transaction->domain = $domain;
                            $transaction->save();


                            $admin->decrement('balance', $total_admin);
                            $user->decrement('balance', $total);

                            $this->sendDiscordNotifications($orderData, $user, $service, $server, $price, $total, $domain, $request);

                            // Commit transaction nếu mọi thứ thành công
                            DB::commit();

                            Log::info("Đơn hàng webcon được tạo thành công", [
                                'order_code' => $orderCode,
                                'user_id' => $user->id,
                                'total' => $total,
                                'domain' => $domain
                            ]);

                            return response()->json([
                                'code' => '200',
                                'status' => 'success',
                                'message' => 'Đơn hàng của bạn đã được tạo thành công!',
                                'data' => [
                                    'id' => $order->id,
                                    'order_code' => $orderCode,
                                    'price' => $price,
                                    'payment' => $total,
                                    'status' => 'Processing',
                                ],
                            ], 200);
                        }

                    } catch (\Exception $e) {
                        // Rollback transaction nếu có lỗi
                        DB::rollback();

                        Log::error("Lỗi tạo đơn hàng webcon", [
                            'error' => $e->getMessage(),
                            'user_id' => $user->id,
                            'domain' => $domain,
                            'trace' => $e->getTraceAsString()
                        ]);

                        return response()->json([
                            'code' => '500',
                            'status' => 'error',
                            'message' => 'Có lỗi xảy ra khi tạo đơn hàng. Vui lòng thử lại sau!',
                        ], 500);
                    }
                } else {
                    return response()->json([ 'code' => '400', 'status' => 'error', 'message' => $result['message'] ?? "Tạo đơn hàng thất bại!",
                    ], 400);
                }
            }
        
    }
    
    function handleFacebookID($link) {
        $url = 'https://nqtam.id.vn/get-id?link=' . urlencode($link);
    
        $response = @file_get_contents($url);
        if ($response === false) {
            return null;
        }
    
        $data = json_decode($response, true);
        if ($data && isset($data['data']['id'])) {
            return $data['data']['id'];
        }
    
        return null;
    }    
    
    /**
     * Làm sạch chuỗi HTML cho Telegram để đảm bảo các thẻ được đóng đúng cách
     *
     * @param string $text Nội dung cần làm sạch
     * @return string Nội dung đã được làm sạch
     */
    private function sanitizeTelegramHTML($text) {
        if (!is_string($text)) {
            return '';
        }
        
        // Đảm bảo các thẻ HTML phổ biến được đóng đúng cách
        $unclosedTags = [
            '<b>' => '</b>',
            '<i>' => '</i>',
            '<u>' => '</u>',
            '<s>' => '</s>',
            '<code>' => '</code>',
            '<pre>' => '</pre>'
        ];
        
        foreach ($unclosedTags as $openTag => $closeTag) {
            $openCount = substr_count(strtolower($text), strtolower($openTag));
            $closeCount = substr_count(strtolower($text), strtolower($closeTag));
            
            // Thêm thẻ đóng nếu thiếu
            if ($openCount > $closeCount) {
                $text .= str_repeat($closeTag, $openCount - $closeCount);
            }
        }
        
        return $text;
    }
    
    /**
     * Gửi thông báo qua Discord
     *
     * @param array $orderData Dữ liệu đơn hàng
     * @param User $user Thông tin người dùng
     * @param Service $service Thông tin dịch vụ
     * @param ServiceServer $server Thông tin máy chủ
     * @param float $price Giá dịch vụ
     * @param float $total Tổng thanh toán
     * @param string $domain Tên miền
     * @param Request $request Request hiện tại
     * @return void
     */
    private function sendDiscordNotifications($orderData, $user, $service, $server, $price, $total, $domain, $request)
    {
        if (site('discord_webhook_url')) {
            try {
                $quantity = $orderData['quantity'] ?? 0;
                
                $notifyMessage = '🛒 **Đơn Hàng Mới Được Tạo Từ Website ' . $domain . '!**' . PHP_EOL . PHP_EOL .
                    '👤 **Khách Hàng:** ' . $user->name . ' (' . $user->email . ')' . PHP_EOL .
                    '🧾 **Mã Đơn Hàng:** ' . $orderData['order_code'] . PHP_EOL .
                    '📦 **Gói Dịch Vụ:** ' . $service->name . PHP_EOL .
                    '🔗 **Link Hoặc UID:** ' . $request->object_id . PHP_EOL .
                    '🔢 **Số Lượng:** ' . number_format($quantity) . PHP_EOL .
                    '🔗 **ID Máy Chủ:** ' . $server->id . PHP_EOL .
                    '💰 **Giá Tiền:** ' . $price . 'đ' . PHP_EOL .
                    '💵 **Số Dư:** ' . number_format($user->balance + $total) . 'đ - ' . number_format($total) . 'đ = ' . number_format($user->balance) . 'đ' . PHP_EOL .
                    '📝 **Ghi Chú:** ' . ($request->note ?? 'Không có ghi chú') . PHP_EOL;

                $discord_notify = new DiscordSdk();
                $discord_notify->botNotify()->sendMessage([
                    'text' => $notifyMessage,
                ]);
            } catch (\Exception $e) {
                \Log::error('Lỗi gửi thông báo Discord: ' . $e->getMessage());
            }
        }

        if ($user->discord_id !== null && $user->notification_discord == 'yes') {
            try {
                $quantity = $orderData['quantity'] ?? 0;
                
                $userMessage = '🛒 **Bạn Vừa Tạo Đơn Hàng Mới Từ Website ' . $domain . '!**' . PHP_EOL . PHP_EOL .
                    '👤 **Khách Hàng:** ' . $user->name . ' (' . $user->email . ')' . PHP_EOL .
                    '🧾 **Mã Đơn Hàng:** ' . $orderData['order_code'] . PHP_EOL .
                    '📦 **Gói Dịch Vụ:** ' . $service->name . PHP_EOL .
                    '🔗 **Link Hoặc UID:** ' . $request->object_id . PHP_EOL .
                    '🔢 **Số Lượng:** ' . number_format($quantity) . PHP_EOL .
                    '🔗 **ID Máy Chủ:** ' . $server->id . PHP_EOL .
                    '💰 **Giá Tiền:** ' . $price . 'đ' . PHP_EOL .
                    '💵 **Số Dư:** ' . number_format($user->balance + $total) . 'đ - ' . number_format($total) . 'đ = ' . number_format($user->balance) . 'đ' . PHP_EOL .
                    '📝 **Ghi Chú:** ' . ($request->note ?? 'Không có ghi chú') . PHP_EOL;

                $discord_chat = new DiscordSdk();
                $discord_chat->botProduct()->sendMessage([
                    'text' => $userMessage,
                ]);
            } catch (\Exception $e) {
                \Log::error('Lỗi gửi tin nhắn Discord cho người dùng: ' . $e->getMessage());
            }
        }
    }

    /**
     * Hoàn tiền đơn hàng
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function refundOrder(Request $request)
    {
        try {
            if (site('maintain') === 'on') {
                return response()->json([
                    'code' => '401',
                    'status' => 'error',
                    'message' => 'Hệ thống đang bảo trì, vui lòng quay lại sau!',
                ], 401);
            }

            $api_token = $request->header('X-Access-Token');

            if (!$api_token) {
                return response()->json([
                    'code' => '401',
                    'status' => 'error',
                    'message' => 'Không tìm thấy X-Access-Token!',
                ], 401);
            }

            $domain = $request->getHost();
            $user = User::where('api_token', $api_token)->where('domain', $domain)->first();

            if (!$user) {
                return response()->json([
                    'code' => '401',
                    'status' => 'error',
                    'message' => 'X-Access-Token không hợp lệ!',
                ], 401);
            }

            if ($user->status !== 'active') {
                return response()->json([
                    'code' => '401',
                    'status' => 'error',
                    'message' => 'Tài khoản của bạn hiện tại không được phép thực hiện hành động này!',
                ], 401);
            }

            $valid = Validator::make($request->all(), [
                'order_id' => 'required|integer',
            ], [
                'order_id.required' => 'Vui lòng nhập ID đơn hàng cần hoàn tiền!',
                'order_id.integer' => 'ID đơn hàng phải là số!',
            ]);

            if ($valid->fails()) {
                return response()->json([
                    'code' => '400',
                    'status' => 'error',
                    'message' => $valid->errors()->first(),
                ], 400);
            }

            // Tìm đơn hàng
            $order = Order::where('id', $request->order_id)
                          ->where('user_id', $user->id)
                          ->where('domain', $domain)
                          ->first();

            if (!$order) {
                return response()->json([
                    'code' => '404',
                    'status' => 'error',
                    'message' => 'Không tìm thấy đơn hàng!',
                ], 404);
            }

            // Kiểm tra trạng thái đơn hàng có thể hoàn tiền
            if (!in_array($order->status, ['Processing', 'Partial', 'Pending'])) {
                return response()->json([
                    'code' => '400',
                    'status' => 'error',
                    'message' => 'Đơn hàng này không thể hoàn tiền!',
                ], 400);
            }

            // Kiểm tra xem đơn hàng đã được hoàn tiền chưa
            if ($order->status === 'Refunded') {
                return response()->json([
                    'code' => '400',
                    'status' => 'error',
                    'message' => 'Đơn hàng này đã được hoàn tiền!',
                ], 400);
            }

            // Tính toán số tiền hoàn trả
            $refundAmount = $order->payment;
            
            // Thực hiện hoàn tiền
            $order->status = 'Refunded';
            $order->save();

            // Cộng tiền vào tài khoản người dùng
            $user->increment('balance', $refundAmount);

            // Tạo giao dịch hoàn tiền
            $transaction = new Transaction();
            $transaction->user_id = $user->id;
            $transaction->tran_code = 'REFUND_' . $order->order_code;
            $transaction->type = 'refund';
            $transaction->action = 'add';
            $transaction->first_balance = $refundAmount;
            $transaction->before_balance = $user->balance - $refundAmount;
            $transaction->after_balance = $user->balance;
            $transaction->note = 'Hoàn tiền đơn hàng ' . $order->order_code;
            $transaction->ip = $request->ip();
            $transaction->domain = $domain;
            $transaction->save();

            // Gửi thông báo Telegram nếu có
            if (site('discord_webhook_url')) {
                try {
                    $discord_notify = new DiscordSdk();
                    $bot_notify->botNotify()->sendMessage([
                        'chat_id' => site('telegram_chat_id'),
                        'text' => $this->sanitizeTelegramHTML('💰 <b>' . $domain . ' - Hoàn tiền đơn hàng' . "</b>\n" .
                            '👤 <b>Khách hàng:</b> ' . $user->username . "\n" .
                            '🧾 <b>Mã đơn hàng:</b> ' . $order->order_code . "\n" .
                            '💵 <b>Số tiền hoàn:</b> ' . number_format($refundAmount) . 'đ' . "\n" .
                            '💰 <b>Số dư mới:</b> ' . number_format($user->balance) . 'đ' . "\n"),
                        'parse_mode' => 'HTML',
                    ]);
                } catch (\Exception $e) {
                    \Log::error('Lỗi gửi thông báo Telegram cho hoàn tiền: ' . $e->getMessage());
                }
            }

            return response()->json([
                'code' => '200',
                'status' => 'success',
                'message' => 'Hoàn tiền đơn hàng thành công!',
                'data' => [
                    'order_id' => $order->id,
                    'order_code' => $order->order_code,
                    'refund_amount' => $refundAmount,
                    'new_balance' => $user->balance,
                ],
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'code' => '500',
                'status' => 'error',
                'message' => 'Lỗi hệ thống: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Bảo hành đơn hàng
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function warrantyOrder(Request $request)
    {
        try {
            if (site('maintain') === 'on') {
                return response()->json([
                    'code' => '401',
                    'status' => 'error',
                    'message' => 'Hệ thống đang bảo trì, vui lòng quay lại sau!',
                ], 401);
            }

            $api_token = $request->header('X-Access-Token');

            if (!$api_token) {
                return response()->json([
                    'code' => '401',
                    'status' => 'error',
                    'message' => 'Không tìm thấy X-Access-Token!',
                ], 401);
            }

            $domain = $request->getHost();
            $user = User::where('api_token', $api_token)->where('domain', $domain)->first();

            if (!$user) {
                return response()->json([
                    'code' => '401',
                    'status' => 'error',
                    'message' => 'X-Access-Token không hợp lệ!',
                ], 401);
            }

            if ($user->status !== 'active') {
                return response()->json([
                    'code' => '401',
                    'status' => 'error',
                    'message' => 'Tài khoản của bạn hiện tại không được phép thực hiện hành động này!',
                ], 401);
            }

            $valid = Validator::make($request->all(), [
                'order_id' => 'required|integer',
                'reason' => 'required|string|max:500',
            ], [
                'order_id.required' => 'Vui lòng nhập ID đơn hàng cần bảo hành!',
                'order_id.integer' => 'ID đơn hàng phải là số!',
                'reason.required' => 'Vui lòng nhập lý do bảo hành!',
                'reason.string' => 'Lý do bảo hành phải là chuỗi văn bản!',
                'reason.max' => 'Lý do bảo hành không được vượt quá 500 ký tự!',
            ]);

            if ($valid->fails()) {
                return response()->json([
                    'code' => '400',
                    'status' => 'error',
                    'message' => $valid->errors()->first(),
                ], 400);
            }

            // Tìm đơn hàng
            $order = Order::where('id', $request->order_id)
                          ->where('user_id', $user->id)
                          ->where('domain', $domain)
                          ->first();

            if (!$order) {
                return response()->json([
                    'code' => '404',
                    'status' => 'error',
                    'message' => 'Không tìm thấy đơn hàng!',
                ], 404);
            }

            // Kiểm tra trạng thái đơn hàng có thể bảo hành
            if (!in_array($order->status, ['Success', 'Completed'])) {
                return response()->json([
                    'code' => '400',
                    'status' => 'error',
                    'message' => 'Chỉ có thể bảo hành đơn hàng đã hoàn thành!',
                ], 400);
            }

            // Kiểm tra thời gian bảo hành (ví dụ: 30 ngày)
            $warrantyDays = 30;
            $orderDate = Carbon::parse($order->created_at);
            $currentDate = Carbon::now();
            
            if ($currentDate->diffInDays($orderDate) > $warrantyDays) {
                return response()->json([
                    'code' => '400',
                    'status' => 'error',
                    'message' => 'Đơn hàng này đã quá thời hạn bảo hành (' . $warrantyDays . ' ngày)!',
                ], 400);
            }

            // Kiểm tra xem đơn hàng đã có yêu cầu bảo hành chưa
            if ($order->warranty_status === 'Processing') {
                return response()->json([
                    'code' => '400',
                    'status' => 'error',
                    'message' => 'Đơn hàng này đã có yêu cầu bảo hành đang được xử lý!',
                ], 400);
            }

            // Cập nhật thông tin bảo hành
            $order->warranty_status = 'Processing';
            $order->warranty_reason = $request->reason;
            $order->warranty_date = now();
            $order->save();

            // Tạo giao dịch bảo hành (để theo dõi)
            $transaction = new Transaction();
            $transaction->user_id = $user->id;
            $transaction->tran_code = 'WARRANTY_' . $order->order_code;
            $transaction->type = 'warranty';
            $transaction->action = 'warranty_request';
            $transaction->first_balance = 0;
            $transaction->before_balance = $user->balance;
            $transaction->after_balance = $user->balance;
            $transaction->note = 'Yêu cầu bảo hành đơn hàng ' . $order->order_code . ' - Lý do: ' . $request->reason;
            $transaction->ip = $request->ip();
            $transaction->domain = $domain;
            $transaction->save();

            // Gửi thông báo Telegram cho admin
            if (site('discord_webhook_url')) {
                try {
                    $discord_notify = new DiscordSdk();
                    $bot_notify->botNotify()->sendMessage([
                        'chat_id' => site('telegram_chat_id'),
                        'text' => $this->sanitizeTelegramHTML('🔧 <b>' . $domain . ' - Yêu cầu bảo hành đơn hàng' . "</b>\n" .
                            '👤 <b>Khách hàng:</b> ' . $user->username . "\n" .
                            '🧾 <b>Mã đơn hàng:</b> ' . $order->order_code . "\n" .
                            '📝 <b>Lý do bảo hành:</b> ' . $request->reason . "\n" .
                            '📅 <b>Ngày tạo đơn:</b> ' . $order->created_at->format('d/m/Y H:i:s') . "\n" .
                            '⏰ <b>Ngày yêu cầu:</b> ' . now()->format('d/m/Y H:i:s') . "\n"),
                        'parse_mode' => 'HTML',
                    ]);
                } catch (\Exception $e) {
                    \Log::error('Lỗi gửi thông báo Telegram cho bảo hành: ' . $e->getMessage());
                }
            }

            // Gửi thông báo cho người dùng nếu có telegram_id
            if ($user->telegram_id !== null && $user->notification_telegram) {
                try {
                    $bot_chat = new TelegramSdk();
                    $bot_chat->botChat()->sendMessage([
                        'chat_id' => $user->telegram_id,
                        'text' => $this->sanitizeTelegramHTML('🔧 <b>Yêu cầu bảo hành đã được gửi!</b>' . "\n\n" .
                            '🧾 <b>Mã đơn hàng:</b> ' . $order->order_code . "\n" .
                            '📝 <b>Lý do bảo hành:</b> ' . $request->reason . "\n" .
                            '⏰ <b>Thời gian:</b> ' . now()->format('d/m/Y H:i:s') . "\n" .
                            '📞 <b>Trạng thái:</b> Đang được xử lý' . "\n\n" .
                            'Chúng tôi sẽ xem xét và phản hồi sớm nhất có thể!'),
                        'parse_mode' => 'HTML',
                    ]);
                } catch (\Exception $e) {
                    \Log::error('Lỗi gửi tin nhắn Telegram cho người dùng về bảo hành: ' . $e->getMessage());
                }
            }

            return response()->json([
                'code' => '200',
                'status' => 'success',
                'message' => 'Yêu cầu bảo hành đã được gửi thành công!',
                'data' => [
                    'order_id' => $order->id,
                    'order_code' => $order->order_code,
                    'warranty_reason' => $request->reason,
                    'warranty_date' => $order->warranty_date,
                    'warranty_status' => 'Processing',
                ],
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'code' => '500',
                'status' => 'error',
                'message' => 'Lỗi hệ thống: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Cập nhật đơn hàng
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateOrder(Request $request)
    {
        try {
            if (site('maintain') === 'on') {
                return response()->json([
                    'code' => '401',
                    'status' => 'error',
                    'message' => 'Hệ thống đang bảo trì, vui lòng quay lại sau!',
                ], 401);
            }

            $api_token = $request->header('X-Access-Token');

            if (!$api_token) {
                return response()->json([
                    'code' => '401',
                    'status' => 'error',
                    'message' => 'Không tìm thấy X-Access-Token!',
                ], 401);
            }

            $domain = $request->getHost();
            $user = User::where('api_token', $api_token)->where('domain', $domain)->first();

            if (!$user) {
                return response()->json([
                    'code' => '401',
                    'status' => 'error',
                    'message' => 'X-Access-Token không hợp lệ!',
                ], 401);
            }

            if ($user->status !== 'active') {
                return response()->json([
                    'code' => '401',
                    'status' => 'error',
                    'message' => 'Tài khoản của bạn hiện tại không được phép thực hiện hành động này!',
                ], 401);
            }

            $valid = Validator::make($request->all(), [
                'order_id' => 'required|integer',
            ], [
                'order_id.required' => 'Vui lòng nhập ID đơn hàng cần cập nhật!',
                'order_id.integer' => 'ID đơn hàng phải là số!',
            ]);

            if ($valid->fails()) {
                return response()->json([
                    'code' => '400',
                    'status' => 'error',
                    'message' => $valid->errors()->first(),
                ], 400);
            }

            // Tìm đơn hàng
            $order = Order::where('id', $request->order_id)
                          ->where('user_id', $user->id)
                          ->where('domain', $domain)
                          ->first();

            if (!$order) {
                return response()->json([
                    'code' => '404',
                    'status' => 'error',
                    'message' => 'Không tìm thấy đơn hàng!',
                ], 404);
            }

            // Kiểm tra trạng thái đơn hàng có thể cập nhật
            if (!in_array($order->status, ['Pending', 'Processing', 'Partial'])) {
                return response()->json([
                    'code' => '400',
                    'status' => 'error',
                    'message' => 'Đơn hàng này không thể cập nhật!',
                ], 400);
            }

            $updateData = [];
            $hasChanges = false;

            // Kiểm tra các trường có thể cập nhật
            if ($request->has('note') && $request->note !== $order->note) {
                $updateData['note'] = $request->note;
                $hasChanges = true;
            }

            if ($request->has('object_id') && $request->object_id !== $order->object_id) {
                // Validate object_id
                $validObjectId = Validator::make($request->all(), [
                    'object_id' => 'required|string|max:255',
                ], [
                    'object_id.required' => 'Vui lòng nhập UID hoặc Link!',
                    'object_id.string' => 'UID hoặc Link phải là chuỗi văn bản!',
                    'object_id.max' => 'UID hoặc Link không được vượt quá 255 ký tự!',
                ]);

                if ($validObjectId->fails()) {
                    return response()->json([
                        'code' => '400',
                        'status' => 'error',
                        'message' => $validObjectId->errors()->first(),
                    ], 400);
                }

                $updateData['object_id'] = $request->object_id;
                $hasChanges = true;
            }

            if ($request->has('comments') && $request->comments !== html_entity_decode($order->comments)) {
                $updateData['comments'] = htmlentities($request->comments);
                $hasChanges = true;
            }

            if ($request->has('reaction') && $request->reaction !== $order->reaction) {
                $updateData['reaction'] = $request->reaction;
                $hasChanges = true;
            }

            if (!$hasChanges) {
                return response()->json([
                    'code' => '400',
                    'status' => 'error',
                    'message' => 'Không có thông tin nào được thay đổi!',
                ], 400);
            }

            // Cập nhật đơn hàng
            foreach ($updateData as $key => $value) {
                $order->$key = $value;
            }
            $order->updated_at = now();
            $order->save();

            // Cập nhật order_data nếu cần
            $orderData = json_decode($order->order_data, true);
            foreach ($updateData as $key => $value) {
                if (isset($orderData[$key])) {
                    $orderData[$key] = $value;
                }
            }
            $order->order_data = json_encode($orderData);
            $order->save();

            // Tạo giao dịch theo dõi
            $transaction = new Transaction();
            $transaction->user_id = $user->id;
            $transaction->tran_code = 'UPDATE_' . $order->order_code;
            $transaction->type = 'update';
            $transaction->action = 'update_order';
            $transaction->first_balance = 0;
            $transaction->before_balance = $user->balance;
            $transaction->after_balance = $user->balance;
            $transaction->note = 'Cập nhật đơn hàng ' . $order->order_code;
            $transaction->ip = $request->ip();
            $transaction->domain = $domain;
            $transaction->save();

            // Gửi thông báo Telegram cho admin nếu có thay đổi quan trọng
            if ((isset($updateData['object_id']) || isset($updateData['comments'])) && site('telegram_bot_token') && site('telegram_chat_id')) {
                try {
                    $changesList = [];
                    if (isset($updateData['object_id'])) {
                        $changesList[] = 'Link/UID: ' . $updateData['object_id'];
                    }
                    if (isset($updateData['comments'])) {
                        $changesList[] = 'Comments: ' . strip_tags(html_entity_decode($updateData['comments']));
                    }
                    if (isset($updateData['reaction'])) {
                        $changesList[] = 'Reaction: ' . $updateData['reaction'];
                    }
                    if (isset($updateData['note'])) {
                        $changesList[] = 'Note: ' . $updateData['note'];
                    }

                    $discord_notify = new DiscordSdk();
                    $bot_notify->botNotify()->sendMessage([
                        'chat_id' => site('telegram_chat_id'),
                        'text' => $this->sanitizeTelegramHTML('📝 <b>' . $domain . ' - Cập nhật đơn hàng' . "</b>\n" .
                            '👤 <b>Khách hàng:</b> ' . $user->username . "\n" .
                            '🧾 <b>Mã đơn hàng:</b> ' . $order->order_code . "\n" .
                            '📋 <b>Thay đổi:</b> ' . implode(', ', $changesList) . "\n" .
                            '⏰ <b>Thời gian:</b> ' . now()->format('d/m/Y H:i:s') . "\n"),
                        'parse_mode' => 'HTML',
                    ]);
                } catch (\Exception $e) {
                    \Log::error('Lỗi gửi thông báo Telegram cho cập nhật đơn hàng: ' . $e->getMessage());
                }
            }

            return response()->json([
                'code' => '200',
                'status' => 'success',
                'message' => 'Cập nhật đơn hàng thành công!',
                'data' => [
                    'order_id' => $order->id,
                    'order_code' => $order->order_code,
                    'updated_fields' => array_keys($updateData),
                    'updated_at' => $order->updated_at,
                ],
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'code' => '500',
                'status' => 'error',
                'message' => 'Lỗi hệ thống: ' . $e->getMessage(),
            ], 500);
        }
    }
}

